import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'login_screen.dart';
import '../main.dart';

class AuthWrapper extends StatelessWidget {
  const AuthWrapper({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        // إذا كان هناك اتصال (أي حالة المصادقة قيد التحميل)
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }
        
        // إذا كان المستخدم مسجل الدخول وتم تفعيل البريد الإلكتروني
        if (snapshot.hasData && snapshot.data != null && snapshot.data!.emailVerified) {
          // طباعة معلومات لتتبع حالة التسجيل
          print('المستخدم مسجل الدخول وتم تفعيل البريد الإلكتروني: ${snapshot.data?.uid}');
          // استخدام MainNavigation دون إعطاء مفتاح
          return MainNavigation();
        }
        
        // إذا كان المستخدم مسجل لكن البريد الإلكتروني غير مفعل
        if (snapshot.hasData && snapshot.data != null && !snapshot.data!.emailVerified) {
          // تسجيل خروج المستخدم
          WidgetsBinding.instance.addPostFrameCallback((_) {
            FirebaseAuth.instance.signOut();
          });
          print('المستخدم مسجل لكن البريد الإلكتروني غير مفعل');
        }
        
        // إذا كان المستخدم غير مسجل الدخول أو البريد الإلكتروني غير مفعل
        print('المستخدم غير مسجل الدخول أو البريد الإلكتروني غير مفعل');
        return const LoginScreen();
      },
    );
  }
}
