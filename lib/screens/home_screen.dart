import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/services.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../main.dart'; // Import for AppNavigation

class HomeScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  bool _isAnimating = false;

  @override
  void initState() {
    super.initState();
    
    // التحقق من حالة تفعيل البريد الإلكتروني
    _checkEmailVerification();
    
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 1000),
    );
    
    // Animation for scaling the text
    _scaleAnimation = Tween<double>(begin: 0.7, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController, 
        curve: Interval(0.0, 0.6, curve: Curves.elasticOut),
      ),
    );
    
    // Animation for fading the background
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController, 
        curve: Interval(0.0, 0.4, curve: Curves.easeOut),
      ),
    );
    
    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        // Reset the animation and navigate after a short delay
        Future.delayed(Duration(milliseconds: 600), () {
          AppNavigation.navigateToQuizTab();
          // Reset for next use
          _animationController.reset();
          setState(() {
            _isAnimating = false;
          });
        });
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color.fromARGB(255, 255, 255, 255), // Light gray background
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Stack(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      "الصفحة الرئيسية",
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Colors.black,
                      ),
                    ),
                    SizedBox(height: 20),
                    Text(
                      "حول ملفات PDF إلى اختبارات",
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF30BEA2),
                      ),
                    ),
                    SizedBox(height: 20),
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            _buildFeatureCard(
                              title: "تحويل PDF إلى اختبار",
                              description: "قم بتحميل ملف PDF واترك الذكاء الاصطناعي يقوم بتحويله إلى اختبار تفاعلي",
                              icon: Icons.picture_as_pdf,
                              onTap: () => _selectPdf(context),
                            ),
                            SizedBox(height: 20),
                            _buildInfoCard(
                              title: "كيف يعمل التطبيق؟",
                              items: [
                                "1. اختر ملف PDF من جهازك",
                                "2. يقوم الذكاء الاصطناعي بتحليل المحتوى",
                                "3. يتم إنشاء اختبار تفاعلي من المحتوى",
                                "4. أجب على الأسئلة واحصل على نتيجتك",
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                
                // Modify the Overlay animation when button is pressed
                if (_isAnimating)
                  AnimatedBuilder(
                    animation: _animationController,
                    builder: (context, child) {
                      return Scaffold(
                        backgroundColor: Color(0xFF30BEA2),
                        body: Center(
                          child: Opacity(
                            opacity: _fadeAnimation.value,
                            child: ScaleTransition(
                              scale: _scaleAnimation,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Text(
                                    "لنبدأ إنشاء الاختبارات",
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 32,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                      shadows: [
                                        Shadow(
                                          blurRadius: 10.0,
                                          color: Colors.black.withOpacity(0.3),
                                          offset: Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 30),
                                  _buildAnimatedDots(),
                                ],
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // التحقق من حالة تفعيل البريد الإلكتروني
  void _checkEmailVerification() {
    final user = FirebaseAuth.instance.currentUser;
    if (user != null && !user.emailVerified) {
      // إذا كان المستخدم موجود لكن البريد الإلكتروني غير مؤكد، أرسله لشاشة البداية
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.of(context).pushReplacementNamed('/auth');
      });
    }
  }

  Widget _buildFeatureCard({
    required String title,
    required String description,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF30BEA2), Color(0xFF3CC7AA)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Color(0xFF30BEA2).withOpacity(0.3),
              blurRadius: 10,
              offset: Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(
              icon,
              size: 50,
              color: Colors.white,
            ),
            SizedBox(height: 20),
            Text(
              title,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            SizedBox(height: 10),
            Text(
              description,
              style: TextStyle(
                fontSize: 14,
                color: Colors.white.withOpacity(0.9),
              ),
            ),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                  "ابدأ الآن",
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(width: 5),
                Icon(
                  Icons.arrow_forward,
                  color: Colors.white,
                  size: 16,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoCard({
    required String title,
    required List<String> items,
  }) {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 2,
            blurRadius: 5,
            offset: Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Color(0xFF30BEA2),
            ),
          ),
          SizedBox(height: 15),
          ...items.map(
            (item) => Padding(
              padding: const EdgeInsets.only(bottom: 10.0),
              child: Text(
                item,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedDots() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(3, (index) {
        return AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            final delay = index * 0.2;
            final start = 0.3 + delay;
            final end = 0.8 + delay;
            
            final Animation<double> dotAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
              CurvedAnimation(
                parent: _animationController,
                curve: Interval(start > 1.0 ? 1.0 : start, end > 1.0 ? 1.0 : end, curve: Curves.easeInOut),
              ),
            );
            
            return Container(
              margin: EdgeInsets.symmetric(horizontal: 5),
              height: 12 + (8 * dotAnimation.value),
              width: 12 + (8 * dotAnimation.value),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.7 + (0.3 * dotAnimation.value)),
                shape: BoxShape.circle,
              ),
            );
          },
        );
      }),
    );
  }

  Future<void> _selectPdf(BuildContext context) async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['pdf'],
    );

    if (result != null) {
      // Use the AppNavigation helper class to navigate to Quiz tab
      AppNavigation.navigateToQuizTab();
    }
  }
}
