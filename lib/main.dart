import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'screens/home_screen.dart';
import 'screens/quiz_screen.dart';
import 'screens/settings_screen.dart';
import 'screens/auth_wrapper.dart';
import 'screens/splash_screen.dart'; // Add import for the new splash screen

// Global navigation key to access navigator from anywhere
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

// Global key for accessing the tab navigation state
final GlobalKey<_MainNavigationState> mainNavigationKey = GlobalKey<_MainNavigationState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      navigatorKey: navigator<PERSON><PERSON>,
      debugShowCheckedModeBanner: false,
      title: 'Quiz<PERSON><PERSON>',
      theme: ThemeData(
        fontFamily: 'Cairo',
        scaffoldBackgroundColor: Colors.white,
        primaryColor: Color(0xFF30BEA2),
        colorScheme: ColorScheme.fromSwatch().copyWith(
          secondary: Color(0xFF30BEA2),
        ),
        appBarTheme: AppBarTheme(
          titleTextStyle: TextStyle(
            fontFamily: 'Cairo',
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        textTheme: TextTheme(
          // تعريف كل أنواع النصوص باستخدام خط Cairo
          displayLarge: TextStyle(fontFamily: 'Cairo'),
          displayMedium: TextStyle(fontFamily: 'Cairo'),
          displaySmall: TextStyle(fontFamily: 'Cairo'),
          headlineLarge: TextStyle(fontFamily: 'Cairo'),
          headlineMedium: TextStyle(fontFamily: 'Cairo'),
          headlineSmall: TextStyle(fontFamily: 'Cairo'),
          titleLarge: TextStyle(fontFamily: 'Cairo'),
          titleMedium: TextStyle(fontFamily: 'Cairo'),
          titleSmall: TextStyle(fontFamily: 'Cairo'),
          bodyLarge: TextStyle(fontFamily: 'Cairo'),
          bodyMedium: TextStyle(fontFamily: 'Cairo'),
          bodySmall: TextStyle(fontFamily: 'Cairo'),
          labelLarge: TextStyle(fontFamily: 'Cairo'),
          labelMedium: TextStyle(fontFamily: 'Cairo'),
          labelSmall: TextStyle(fontFamily: 'Cairo'),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: Color(0xFF30BEA2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            padding: EdgeInsets.symmetric(vertical: 14, horizontal: 24),
            textStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, fontFamily: 'Cairo'),
          ),
        ),
      ),
      // استخدام المصادقة المباشرة للتحقق من حالة المستخدم ثم اتخاذ قرار العرض المناسب
      home: _buildHomeScreen(),
      routes: {
        '/home': (context) => MainNavigation(key: mainNavigationKey),
        '/quiz': (context) => QuizScreen(),
        '/auth': (context) => AuthWrapper(),
      },
    );
  }
  
  // دالة لتحديد الشاشة الأولية بناءً على حالة تسجيل الدخول
  Widget _buildHomeScreen() {
    final currentUser = FirebaseAuth.instance.currentUser;
    
    if (currentUser != null && currentUser.emailVerified) {
      // إذا المستخدم مسجل دخول وتم تفعيل البريد الإلكتروني، انتقل مباشرة إلى الشاشة الرئيسية
      // استخدام نفس mainNavigationKey للحفاظ على الاتساق
      return MainNavigation(key: mainNavigationKey); 
    } else {
      // إذا المستخدم غير مسجل أو البريد الإلكتروني غير مفعل، أظهر شاشة البداية
      return SplashScreen();
    }
  }
}

class MainNavigation extends StatefulWidget {
  MainNavigation({Key? key}) : super(key: key);
  
  @override
  _MainNavigationState createState() => _MainNavigationState();
  
  // NOTE: لم نعد بحاجة إلى هذه الدالة لأننا نستخدم mainNavigationKey مباشرة
}

class _MainNavigationState extends State<MainNavigation> with TickerProviderStateMixin {
  int _currentIndex = 1;  // Default to Home screen (index 1)
  late AnimationController _animationController;
  late AnimationController _fabAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fabScaleAnimation;
  
  final List<Widget> _screens = [
    SettingsScreen(),
    HomeScreen(),    // Index 1 - Home
    QuizScreen(),    // Index 2 - Quiz
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(milliseconds: 400),
      vsync: this,
    );
    
    _fabAnimationController = AnimationController(
      duration: Duration(milliseconds: 200),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: Offset(0.0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
    
    _fabScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _fabAnimationController,
      curve: Curves.elasticOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _fabAnimationController.dispose();
    super.dispose();
  }
  
  // Method to change the current tab programmatically with animation
  void changeTab(int index) {
    if (index >= 0 && index < _screens.length && index != _currentIndex) {
      _animationController.reset();
      setState(() {
        _currentIndex = index;
      });
      _animationController.forward();
    }
  }

  // Special animation method for the floating action button
  void _animateToQuizTab() {
    if (_currentIndex != 2) {
      // تشغيل انيميشن الزر أولاً
      _fabAnimationController.forward().then((_) {
        _fabAnimationController.reverse();
      });
      
      // ثم تشغيل انيميشن تغيير الشاشة
      _animationController.reset();
      setState(() {
        _currentIndex = 2; // Navigate to QuizScreen (index 2)
      });
      _animationController.forward();
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: null,
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: _screens[_currentIndex],
            ),
          );
        },
      ),
      floatingActionButton: AnimatedBuilder(
        animation: _fabScaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _fabScaleAnimation.value,
            child: Container(
              height: 48,
              width: 48,
              child: FloatingActionButton(
                onPressed: () {
                  // إضافة انيميشن خاص لزر الزائد
                  _animateToQuizTab();
                },
                backgroundColor: Color(0xFF30BEA2),
                child: AnimatedBuilder(
                  animation: _animationController,
                  builder: (context, child) {
                    return Transform.rotate(
                      angle: _currentIndex == 2 ? 0.785398 : 0, // 45 degrees in radians when quiz is active
                      child: Icon(Icons.add, color: Colors.white, size: 26),
                    );
                  },
                ),
                elevation: 0,
                shape: CircleBorder(),
              ),
            ),
          );
        },
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      bottomNavigationBar: Container(
        height: 90, 
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: const Color.fromARGB(255, 54, 54, 54).withOpacity(0.1),
              blurRadius: 15,
              offset: Offset(0, -3),
            ),
          ],
        ),
        child: ClipPath(
          clipper: CustomNavClipper(),
          child: BottomAppBar(
            shape: CircularNotchedRectangle(),
            notchMargin: 8,
            color: Colors.white,
            height:90, 
            elevation: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                // Settings tab
                Expanded(
                  child: InkWell(
                    onTap: () {
                      if (_currentIndex != 0) {
                        _animationController.reset();
                        setState(() {
                          _currentIndex = 0;
                        });
                        _animationController.forward();
                      }
                    },
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    child: Container(
                      height: 45,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          AnimatedContainer(
                            duration: Duration(milliseconds: 200),
                            padding: EdgeInsets.all(_currentIndex == 0 ? 6 : 5),
                            decoration: BoxDecoration(
                              color: _currentIndex == 0 ? Color(0xFF30BEA2).withOpacity(0.1) : Colors.transparent,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.person,
                              color: _currentIndex == 0 ? Color(0xFF30BEA2) : Colors.grey,
                              size: 20,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                // Empty space for the FAB
                Expanded(child: SizedBox()),
                // Home tab
                Expanded(
                  child: InkWell(
                    onTap: () {
                      if (_currentIndex != 1) {
                        _animationController.reset();
                        setState(() {
                          _currentIndex = 1;
                        });
                        _animationController.forward();
                      }
                    },
                    splashColor: Colors.transparent,
                    highlightColor: Colors.transparent,
                    child: Container(
                      height: 45,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          AnimatedContainer(
                            duration: Duration(milliseconds: 200),
                            padding: EdgeInsets.all(_currentIndex == 1 ? 6 : 5),
                            decoration: BoxDecoration(
                              color: _currentIndex == 1 ? Color(0xFF30BEA2).withOpacity(0.1) : Colors.transparent,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.home,
                              color: _currentIndex == 1 ? Color(0xFF30BEA2) : Colors.grey,
                              size: 20,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Custom clipper to create a curved bottom navigation bar
class CustomNavClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    Path path = Path();
    final double centerWidth = size.width / 2;
    final double notchRadius = 38; // Increased for a wider, smoother notch
    final double notchDepth = 22; // Decreased for a shallower, more modern notch
    final double edgeHeight = 12; // Decreased for a subtler top edge curve
    
    // Start from top left
    path.moveTo(0, edgeHeight);
    
    // Draw top left corner down to bottom left
    path.lineTo(0, size.height);
    // Draw bottom edge to notch start
    path.lineTo(centerWidth - notchRadius, size.height);
    
    // Draw the notch as a curved path
    path.quadraticBezierTo(
      centerWidth, 
      size.height + notchDepth, // Control point - pulls the curve down
      centerWidth + notchRadius, 
      size.height // End point
    );
    
    // Draw bottom edge to bottom right
    path.lineTo(size.width, size.height);
    // Draw right edge to top right
    path.lineTo(size.width, edgeHeight);
    
    // Draw curved top edge with slight elevation on sides
    path.quadraticBezierTo(
      size.width * 0.75, 
      0, // Control point for right curve
      centerWidth + notchRadius * 1.5, 
      0 // End of right curve
    );
    
    // Draw top edge dip in the middle
    path.quadraticBezierTo(
      centerWidth, 
      0, // Control point - center top
      centerWidth - notchRadius * 1.5, 
      0 // End of center dip
    );
    
    // Finish the left curve of the top edge
    path.quadraticBezierTo(
      size.width * 0.25, 
      0, // Control point for left curve
      0, 
      edgeHeight // Back to starting point
    );
    
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}

// Helper class for navigating between tabs from anywhere in the app
class AppNavigation {
  static void navigateToQuizTab() {
    mainNavigationKey.currentState?.changeTab(2);
  }
  
  static void navigateToHomeTab() {
    mainNavigationKey.currentState?.changeTab(1);
  }
  
  static void navigateToSettingsTab() {
    mainNavigationKey.currentState?.changeTab(0);
  }
}
